import { useAccount } from 'wagmi';
import { useAuth } from '../../context/AuthContext';
import { useState } from 'preact/hooks';
import { getMobileLogs, clearMobileLogs, isMobileDevice } from '../../utils/mobile-utils';

export function MobileDebugInfo() {
  const { address, isConnected, connector } = useAccount();
  const { isAuthenticated, isLoading, error } = useAuth();
  const [showDebug, setShowDebug] = useState(false);
  const [showLogs, setShowLogs] = useState(false);

  // Only show on mobile devices
  const isMobile = isMobileDevice();

  if (!isMobile) {
    return null;
  }

  const logs = getMobileLogs();

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setShowDebug(!showDebug)}
        className="bg-blue-600 text-white px-3 py-2 rounded text-xs pixelated-text"
      >
        Debug
      </button>
      
      {showDebug && (
        <div className="absolute bottom-12 right-0 bg-black/90 text-white p-4 rounded-lg text-xs max-w-xs">
          <div className="space-y-2">
            <div>
              <strong>Wallet Status:</strong>
              <div>Connected: {isConnected ? 'Yes' : 'No'}</div>
              <div>Address: {address ? `${address.slice(0, 6)}...${address.slice(-4)}` : 'None'}</div>
              <div>Connector: {connector?.name || 'None'}</div>
            </div>
            
            <div>
              <strong>Auth Status:</strong>
              <div>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</div>
              <div>Loading: {isLoading ? 'Yes' : 'No'}</div>
              <div>Error: {error || 'None'}</div>
            </div>
            
            <div>
              <strong>Browser Info:</strong>
              <div>User Agent: {navigator.userAgent.slice(0, 50)}...</div>
              <div>Mobile: {isMobile ? 'Yes' : 'No'}</div>
            </div>
            
            <div>
              <strong>Storage:</strong>
              <div>Token: {localStorage.getItem('accessToken') ? 'Present' : 'None'}</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
