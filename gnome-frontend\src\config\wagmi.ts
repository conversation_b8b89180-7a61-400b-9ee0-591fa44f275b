import { http, createConfig } from 'wagmi';
import { mainnet, baseSepolia, base } from 'wagmi/chains';
import { injected, metaMask, coinbaseWallet, walletConnect } from 'wagmi/connectors';

// Get required chain ID from environment variable
const requiredChainId = parseInt(import.meta.env.VITE_REQUIRED_CHAIN_ID || '84532');

// Configure supported chains - ensure at least one chain is always present
const supportedChains = [baseSepolia, base, mainnet] as const;

// Get the required chain
const requiredChain = supportedChains.find(chain => chain.id === requiredChainId) || baseSepolia;

// Configure chains and providers
export const config = createConfig({
  chains: supportedChains,
  transports: {
    [baseSepolia.id]: http(),
    [base.id]: http(),
    [mainnet.id]: http(),
  },
  connectors: [
    metaMask(),
    injected(),
    coinbaseWallet({
      appName: 'The Cave',
    }),
    walletConnect({
      projectId: 'YOUR_PROJECT_ID', // Replace with an actual WalletConnect project ID if needed
      metadata: {
        name: 'The Cave',
        description: 'Mining game with Ethereum integration',
        url: window.location.origin,
        icons: [window.location.origin + '/favicon.ico']
      }
    })
  ],
});

// Export required chain info for use in components
export { requiredChain, requiredChainId };
