import { useConnect, useAccount, useDisconnect } from 'wagmi';
import { useEffect } from 'preact/hooks';
import { useAuth } from '../../context/AuthContext';

export function ConnectWallet() {
  const { connect, connectors, isPending } = useConnect();
  
  const { address, isConnected } = useAccount();
  const { disconnect } = useDisconnect();
  const { login, isAuthenticated, isLoading: authLoading } = useAuth();

  // Attempt to authenticate when wallet is connected
  useEffect(() => {
    const attemptLogin = async () => {
      if (isConnected && address && !isAuthenticated && !authLoading) {
        console.log('Wallet connected, attempting login with address:', address);
        await login();
      }
    };

    attemptLogin();
  }, [isConnected, address, isAuthenticated, authLoading, login]);

  // Handle wallet connection
  const handleConnect = async () => {
    try {
      if (isConnected) {
        console.log('Already connected, attempting to authenticate...');
        await login();
        return;
      }
      
      console.log('Connecting wallet...');
      // Use MetaMask connector by default
      const connector = connectors.find(c => c.name === 'MetaMask') || connectors[0];
      
      if (!connector) {
        console.error('No connectors available');
        return;
      }
      
      console.log('Using connector:', connector.name);
      // Connect wallet
      await connect({ connector });
    } catch (error) {
      console.error('Connection failed:', error);
    }
  };

  // Handle wallet disconnection
  const handleDisconnect = () => {
    disconnect();
  };

  return (
    <button 
      className="connect-wallet-btn rounded pixelated-text"
      onClick={isAuthenticated ? handleDisconnect : handleConnect}
      disabled={isPending || authLoading}
    >
      {isPending
        ? 'Connecting...'
        : authLoading
        ? 'Authenticating...'
        : isAuthenticated
        ? 'Disconnect'
        : isConnected
        ? 'Authenticate'
        : 'Connect Wallet'}
    </button>
  );
}
