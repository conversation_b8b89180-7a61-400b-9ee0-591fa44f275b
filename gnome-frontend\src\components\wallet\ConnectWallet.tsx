import { useConnect, useAccount, useDisconnect } from 'wagmi';
import { useEffect, useRef } from 'preact/hooks';
import { useAuth } from '../../context/AuthContext';

export function ConnectWallet() {
  const { connect, connectors, isPending } = useConnect();

  const { address, isConnected } = useAccount();
  const { disconnect } = useDisconnect();
  const { login, isAuthenticated, isLoading: authLoading } = useAuth();
  const loginAttemptedRef = useRef(false);

  // Attempt to authenticate when wallet is connected
  useEffect(() => {
    const attemptLogin = async () => {
      if (isConnected && address && !isAuthenticated && !authLoading && !loginAttemptedRef.current) {
        console.log('Wallet connected, attempting login with address:', address);
        loginAttemptedRef.current = true;

        // Add delay for mobile compatibility
        setTimeout(async () => {
          try {
            await login();
          } catch (error) {
            console.error('Login attempt failed:', error);
            // Reset the flag so user can try again
            loginAttemptedRef.current = false;
          }
        }, 1000);
      }
    };

    attemptLogin();
  }, [isConnected, address, isAuthenticated, authLoading, login]);

  // Reset login attempt flag when wallet disconnects
  useEffect(() => {
    if (!isConnected) {
      loginAttemptedRef.current = false;
    }
  }, [isConnected]);

  // Handle wallet connection
  const handleConnect = async () => {
    try {
      if (isConnected) {
        console.log('Already connected, attempting to authenticate...');
        loginAttemptedRef.current = false; // Reset flag to allow manual authentication
        await login();
        return;
      }

      console.log('Connecting wallet...');

      // Always use MetaMask connector
      const connector = connectors.find(c => c.name === 'MetaMask') || connectors[0];

      if (!connector) {
        console.error('No connectors available');
        return;
      }

      console.log('Using connector:', connector.name);
      // Reset login attempt flag before connecting
      loginAttemptedRef.current = false;

      // Connect wallet
      await connect({ connector });
    } catch (error) {
      console.error('Connection failed:', error);
      // Reset flag on error so user can try again
      loginAttemptedRef.current = false;
    }
  };

  // Handle wallet disconnection
  const handleDisconnect = () => {
    disconnect();
  };

  return (
    <button
      className="connect-wallet-btn rounded pixelated-text"
      onClick={isAuthenticated ? handleDisconnect : handleConnect}
      disabled={isPending || authLoading}
    >
      {isPending
        ? 'Connecting...'
        : authLoading
        ? 'Authenticating...'
        : isAuthenticated
        ? 'Disconnect'
        : isConnected
        ? 'Authenticate'
        : 'Connect Wallet'}
    </button>
  );
}
