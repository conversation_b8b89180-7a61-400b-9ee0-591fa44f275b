import { useConnect, useAccount, useDisconnect } from 'wagmi';
import { useEffect, useRef } from 'preact/hooks';
import { useAuth } from '../../context/AuthContext';
import { isMobileDevice, mobileLog, mobileDelay, waitForMobileWalletConnection } from '../../utils/mobile-utils';

export function ConnectWallet() {
  const { connect, connectors, isPending } = useConnect();

  const { address, isConnected } = useAccount();
  const { disconnect } = useDisconnect();
  const { login, isAuthenticated, isLoading: authLoading } = useAuth();
  const loginAttemptedRef = useRef(false);

  // Attempt to authenticate when wallet is connected
  useEffect(() => {
    const attemptLogin = async () => {
      // Add additional checks for mobile compatibility
      if (isConnected && address && !isAuthenticated && !authLoading && !loginAttemptedRef.current) {
        mobileLog('Wallet connected, attempting login', { address, isConnected, isAuthenticated });
        loginAttemptedRef.current = true;

        const isMobile = isMobileDevice();

        if (isMobile) {
          // For mobile, wait for wallet connection to be fully established
          mobileLog('Mobile device detected, waiting for stable connection');
          const connectionStable = await waitForMobileWalletConnection(isConnected, address, 3000);

          if (!connectionStable) {
            mobileLog('Mobile wallet connection not stable, skipping auto-login');
            loginAttemptedRef.current = false;
            return;
          }

          // Additional delay for mobile
          await mobileDelay(1500);
        }

        try {
          mobileLog('Attempting login');
          await login();
          mobileLog('Login successful');
        } catch (error) {
          mobileLog('Login attempt failed', error);
          // Reset the flag so user can try again
          loginAttemptedRef.current = false;
        }
      }
    };

    attemptLogin();
  }, [isConnected, address, isAuthenticated, authLoading, login]);

  // Reset login attempt flag when wallet disconnects
  useEffect(() => {
    if (!isConnected) {
      loginAttemptedRef.current = false;
    }
  }, [isConnected]);

  // Handle wallet connection
  const handleConnect = async () => {
    try {
      if (isConnected) {
        console.log('Already connected, attempting to authenticate...');
        loginAttemptedRef.current = false; // Reset flag to allow manual authentication
        await login();
        return;
      }

      mobileLog('Connecting wallet...');
      const isMobile = isMobileDevice();
      mobileLog('Is mobile device:', isMobile);

      // Use MetaMask connector by default, but prefer injected for mobile
      let connector;
      if (isMobile) {
        // On mobile, try injected first (which should be MetaMask mobile)
        connector = connectors.find(c => c.name === 'Injected') ||
                   connectors.find(c => c.name === 'MetaMask') ||
                   connectors[0];
      } else {
        // On desktop, prefer MetaMask
        connector = connectors.find(c => c.name === 'MetaMask') || connectors[0];
      }

      if (!connector) {
        console.error('No connectors available');
        return;
      }

      console.log('Using connector:', connector.name);
      // Reset login attempt flag before connecting
      loginAttemptedRef.current = false;

      // Connect wallet
      await connect({ connector });
    } catch (error) {
      console.error('Connection failed:', error);
      // Reset flag on error so user can try again
      loginAttemptedRef.current = false;
    }
  };

  // Handle wallet disconnection
  const handleDisconnect = () => {
    disconnect();
  };

  return (
    <button
      className="connect-wallet-btn rounded pixelated-text"
      onClick={isAuthenticated ? handleDisconnect : handleConnect}
      disabled={isPending || authLoading}
    >
      {isPending
        ? 'Connecting...'
        : authLoading
        ? 'Authenticating...'
        : isAuthenticated
        ? 'Disconnect'
        : isConnected
        ? 'Authenticate'
        : 'Connect Wallet'}
    </button>
  );
}
