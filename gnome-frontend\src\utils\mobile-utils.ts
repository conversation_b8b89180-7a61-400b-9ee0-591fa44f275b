/**
 * Utility functions for handling mobile-specific issues
 */

/**
 * Detect if the current device is mobile
 */
export const isMobileDevice = (): boolean => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

/**
 * Detect if the current browser is MetaMask mobile
 */
export const isMetaMaskMobile = (): boolean => {
  return isMobileDevice() && !!(window as any).ethereum?.isMetaMask;
};

/**
 * Add delay for mobile operations that need extra time
 */
export const mobileDelay = (ms: number = 1000): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Enhanced logging for mobile debugging
 */
export const mobileLog = (message: string, data?: any): void => {
  const timestamp = new Date().toISOString();
  const prefix = `[MOBILE-${timestamp}]`;
  
  if (data) {
    console.log(prefix, message, data);
  } else {
    console.log(prefix, message);
  }
  
  // Also store in localStorage for debugging
  try {
    const logs = JSON.parse(localStorage.getItem('mobileLogs') || '[]');
    logs.push({
      timestamp,
      message,
      data: data ? JSON.stringify(data) : null
    });
    
    // Keep only last 50 logs
    if (logs.length > 50) {
      logs.splice(0, logs.length - 50);
    }
    
    localStorage.setItem('mobileLogs', JSON.stringify(logs));
  } catch (error) {
    console.warn('Failed to store mobile log:', error);
  }
};

/**
 * Get stored mobile logs for debugging
 */
export const getMobileLogs = (): any[] => {
  try {
    return JSON.parse(localStorage.getItem('mobileLogs') || '[]');
  } catch (error) {
    console.warn('Failed to retrieve mobile logs:', error);
    return [];
  }
};

/**
 * Clear mobile logs
 */
export const clearMobileLogs = (): void => {
  localStorage.removeItem('mobileLogs');
};

/**
 * Check if we're in a mobile browser that might have context switching issues
 */
export const hasMobileContextIssues = (): boolean => {
  const userAgent = navigator.userAgent.toLowerCase();
  
  // Known problematic mobile browsers
  return (
    userAgent.includes('android') ||
    userAgent.includes('iphone') ||
    userAgent.includes('ipad')
  );
};

/**
 * Wait for wallet connection to be fully established on mobile
 */
export const waitForMobileWalletConnection = async (
  isConnected: boolean,
  address: string | undefined,
  maxWaitTime: number = 5000
): Promise<boolean> => {
  const startTime = Date.now();
  
  while (Date.now() - startTime < maxWaitTime) {
    if (isConnected && address) {
      mobileLog('Wallet connection confirmed', { isConnected, address });
      return true;
    }
    
    await mobileDelay(100); // Check every 100ms
  }
  
  mobileLog('Wallet connection timeout', { isConnected, address, maxWaitTime });
  return false;
};
